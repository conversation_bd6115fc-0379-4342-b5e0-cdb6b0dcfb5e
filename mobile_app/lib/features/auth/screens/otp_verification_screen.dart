import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'dart:async';

import '../../../core/theme/app_theme.dart';
import '../../../core/providers/auth_provider_minimal.dart';
import '../../../shared/widgets/loading_button.dart';
import '../../../shared/widgets/custom_snackbar.dart';
import '../widgets/test_mode_banner.dart';

class OtpVerificationScreen extends ConsumerStatefulWidget {
  final String phoneNumber;
  final bool isRegistration;
  final String? name;
  final String? email;
  final String? officeName;
  final String? designation;

  const OtpVerificationScreen({
    super.key,
    required this.phoneNumber,
    this.isRegistration = false,
    this.name,
    this.email,
    this.officeName,
    this.designation,
  });

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  final List<TextEditingController> _otpControllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  bool _isLoading = false;
  int _resendTimer = 30;
  Timer? _timer;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _canResend = false;
    _resendTimer = 30;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimer > 0) {
        setState(() => _resendTimer--);
      } else {
        setState(() => _canResend = true);
        timer.cancel();
      }
    });
  }

  String _getOtpCode() {
    return _otpControllers.map((controller) => controller.text).join();
  }

  bool _isOtpComplete() {
    return _getOtpCode().length == 6;
  }

  Future<void> _verifyOtp() async {
    if (!_isOtpComplete()) {
      CustomSnackbar.showError(context, 'Please enter complete OTP');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authNotifier = ref.read(authProvider.notifier);
      bool success;

      if (widget.isRegistration) {
        // For registration, use the registration-specific method
        if (widget.name == null || widget.email == null) {
          throw Exception('Name and email are required for registration');
        }
        success = await authNotifier.verifyRegistrationOTP(
          _getOtpCode(),
          name: widget.name!,
          email: widget.email!,
          officeName: widget.officeName ?? '',
          designation: widget.designation ?? '',
        );

        if (success && mounted) {
          CustomSnackbar.showSuccess(
            context,
            'Registration completed successfully! Welcome ${widget.name}!',
          );
          // Navigate to home screen after successful registration
          context.go('/home');
        }
      } else {
        // For login, use the regular OTP verification
        success = await authNotifier.verifyOTP(_getOtpCode());

        if (success && mounted) {
          CustomSnackbar.showSuccess(context, 'Login successful!');
          // Navigate to home screen after successful login
          context.go('/home');
        }
      }

      if (!success && mounted) {
        CustomSnackbar.showError(context, 'Invalid OTP. Please try again.');
      }
    } catch (e) {
      if (mounted) {
        CustomSnackbar.showError(context, 'Invalid OTP. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _resendOtp() async {
    if (!_canResend) return;

    try {
      final authNotifier = ref.read(authProvider.notifier);
      bool success;

      if (widget.isRegistration) {
        // For registration, resend registration OTP
        if (widget.name == null || widget.email == null) {
          throw Exception('Name and email are required for registration');
        }
        success = await authNotifier.registerWithPhone(
          phoneNumber: widget.phoneNumber,
          name: widget.name!,
          email: widget.email!,
          officeName: widget.officeName ?? '',
          designation: widget.designation ?? '',
        );
      } else {
        // For login, resend login OTP
        success = await authNotifier.sendOTP(widget.phoneNumber);
      }

      if (success && mounted) {
        CustomSnackbar.showSuccess(context, 'OTP sent successfully');
        _startResendTimer();
        // Clear existing OTP
        for (var controller in _otpControllers) {
          controller.clear();
        }
        _focusNodes[0].requestFocus();
      } else if (mounted) {
        CustomSnackbar.showError(context, 'Failed to resend OTP');
      }
    } catch (e) {
      if (mounted) {
        CustomSnackbar.showError(context, 'Failed to resend OTP');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Test Mode Banner
            const TestModeBanner(),
            
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    
                    // Back Button
                    IconButton(
                      onPressed: () => context.pop(),
                      icon: const Icon(Icons.arrow_back),
                      padding: EdgeInsets.zero,
                      alignment: Alignment.centerLeft,
                    ),
                    const SizedBox(height: 20),

                    // Header
                    Text(
                      'Verify OTP',
                      style: GoogleFonts.poppins(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Enter the 6-digit code sent to\n${widget.phoneNumber}',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 48),

                    // OTP Input Fields
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(6, (index) {
                        return SizedBox(
                          width: 45,
                          height: 55,
                          child: TextFormField(
                            controller: _otpControllers[index],
                            focusNode: _focusNodes[index],
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            maxLength: 1,
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppTheme.primaryColor,
                                  width: 2,
                                ),
                              ),
                            ),
                            onChanged: (value) {
                              if (value.isNotEmpty && index < 5) {
                                _focusNodes[index + 1].requestFocus();
                              } else if (value.isEmpty && index > 0) {
                                _focusNodes[index - 1].requestFocus();
                              }

                              if (_isOtpComplete()) {
                                FocusScope.of(context).unfocus();
                              }

                              setState(() {});
                            },
                          ),
                        );
                      }),
                    ),

                    const SizedBox(height: 32),

                    // Verify Button
                    SizedBox(
                      width: double.infinity,
                      child: LoadingButton(
                        onPressed: _isOtpComplete() ? _verifyOtp : null,
                        isLoading: _isLoading || authState.isLoading,
                        text: 'Verify OTP',
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Resend OTP
                    Center(
                      child: _canResend
                          ? TextButton(
                              onPressed: _resendOtp,
                              child: Text(
                                'Resend OTP',
                                style: GoogleFonts.poppins(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            )
                          : Text(
                              'Resend OTP in $_resendTimer seconds',
                              style: GoogleFonts.poppins(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
