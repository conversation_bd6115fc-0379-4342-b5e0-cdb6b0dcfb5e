import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_provider_minimal.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/otp_verification_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/quiz/screens/quiz_list_screen.dart';
import '../../features/quiz/screens/quiz_screen.dart';
import '../../features/quiz/screens/quiz_result_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/settings/screens/settings_screen.dart';

/// App router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    initialLocation: _getInitialLocation(authState),
    redirect: (context, state) => _handleRedirect(authState, state),
    routes: [
      // Onboarding route
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),

      // Auth routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      GoRoute(
        path: '/verify-otp',
        name: 'verify-otp',
        builder: (context, state) {
          final data = state.extra as Map<String, dynamic>;
          return OtpVerificationScreen(
            phoneNumber: data['phoneNumber'] as String,
            isRegistration: data['isRegistration'] as bool? ?? false,
            name: data['name'] as String?,
            email: data['email'] as String?,
            officeName: data['officeName'] as String?,
            designation: data['designation'] as String?,
          );
        },
      ),

      // Main app routes
      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          // Home route
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),

          // Quiz routes
          GoRoute(
            path: '/quiz',
            name: 'quiz-list',
            builder: (context, state) => const QuizListScreen(),
            routes: [
              GoRoute(
                path: ':quizId',
                name: 'quiz',
                builder: (context, state) {
                  final quizId = state.pathParameters['quizId']!;
                  return QuizScreen(quizId: quizId);
                },
                routes: [
                  GoRoute(
                    path: 'result',
                    name: 'quiz-result',
                    builder: (context, state) {
                      final quizId = state.pathParameters['quizId']!;
                      final score = state.extra as int? ?? 0;
                      return QuizResultScreen(
                        quizId: quizId,
                        score: score,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // Profile route
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),

          // Settings route
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
});

/// Get initial location based on auth state
String _getInitialLocation(AuthState authState) {
  if (authState.isLoading) {
    return '/home'; // Will be redirected by redirect logic
  }

  if (authState.isFirstTime) {
    return '/onboarding';
  }

  if (!authState.isAuthenticated) {
    return '/login';
  }

  return '/home';
}

/// Handle route redirects based on auth state
String? _handleRedirect(AuthState authState, GoRouterState state) {
  final isOnboarding = state.matchedLocation == '/onboarding';
  final isLogin = state.matchedLocation == '/login';
  final isRegister = state.matchedLocation == '/register';
  final isVerifyOtp = state.matchedLocation == '/verify-otp';
  final isPublicRoute = isOnboarding || isLogin || isRegister || isVerifyOtp;

  debugPrint(
      'Router redirect check: location=${state.matchedLocation}, isFirstTime=${authState.isFirstTime}, isAuthenticated=${authState.isAuthenticated}, isLoading=${authState.isLoading}');

  // Show loading while auth state is being determined
  // But allow navigation to OTP verification screen and stay on registration/login
  // IMPORTANT: Never redirect during loading if on auth-related screens
  if (authState.isLoading && (isRegister || isLogin || isVerifyOtp)) {
    print(
        'DEBUG: Router - Staying on current route during loading for auth screens');
    return null; // Stay on current route
  }

  if (authState.isLoading && !isVerifyOtp && !isRegister && !isLogin) {
    return null; // Stay on current route
  }

  // Redirect to onboarding if first time
  if (authState.isFirstTime && !isOnboarding) {
    debugPrint('Redirecting to onboarding');
    return '/onboarding';
  }

  // Redirect to login if not authenticated and not on public route
  if (!authState.isAuthenticated && !isPublicRoute) {
    debugPrint('Redirecting to login');
    return '/login';
  }

  // Redirect to home if authenticated and on public route
  // BUT allow staying on OTP verification screen during registration flow
  if (authState.isAuthenticated && isPublicRoute && !isVerifyOtp) {
    debugPrint('Redirecting to home');
    return '/home';
  }

  // No redirect needed
  debugPrint('No redirect needed');
  return null;
}

/// Main shell widget for bottom navigation
class MainShell extends StatelessWidget {
  final Widget child;

  const MainShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: const BottomNavBar(),
    );
  }
}

/// Bottom navigation bar
class BottomNavBar extends ConsumerWidget {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).matchedLocation;

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _getCurrentIndex(currentLocation),
      onTap: (index) => _onTap(context, index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.quiz),
          label: 'Quiz',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'Settings',
        ),
      ],
    );
  }

  int _getCurrentIndex(String location) {
    if (location.startsWith('/home')) return 0;
    if (location.startsWith('/quiz')) return 1;
    if (location.startsWith('/profile')) return 2;
    if (location.startsWith('/settings')) return 3;
    return 0;
  }

  void _onTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.goNamed('home');
        break;
      case 1:
        context.goNamed('quiz-list');
        break;
      case 2:
        context.goNamed('profile');
        break;
      case 3:
        context.goNamed('settings');
        break;
    }
  }
}

/// Error screen for routing errors
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({
    super.key,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Something went wrong!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error?.toString() ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.goNamed('home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Route names for easy access
class AppRoutes {
  static const String onboarding = 'onboarding';
  static const String login = 'login';
  static const String home = 'home';
  static const String quizList = 'quiz-list';
  static const String quiz = 'quiz';
  static const String quizResult = 'quiz-result';
  static const String profile = 'profile';
  static const String settings = 'settings';
}

/// Extension for easy navigation
extension GoRouterExtension on BuildContext {
  /// Navigate to onboarding
  void goToOnboarding() => goNamed(AppRoutes.onboarding);

  /// Navigate to login
  void goToLogin() => goNamed(AppRoutes.login);

  /// Navigate to home
  void goToHome() => goNamed(AppRoutes.home);

  /// Navigate to quiz list
  void goToQuizList() => goNamed(AppRoutes.quizList);

  /// Navigate to specific quiz
  void goToQuiz(String quizId) => goNamed(
        AppRoutes.quiz,
        pathParameters: {'quizId': quizId},
      );

  /// Navigate to quiz result
  void goToQuizResult(String quizId, int score) => goNamed(
        AppRoutes.quizResult,
        pathParameters: {'quizId': quizId},
        extra: score,
      );

  /// Navigate to profile
  void goToProfile() => goNamed(AppRoutes.profile);

  /// Navigate to settings
  void goToSettings() => goNamed(AppRoutes.settings);
}
